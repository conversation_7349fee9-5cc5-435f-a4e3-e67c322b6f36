

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_cron" WITH SCHEMA "pg_catalog";






CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."add_author_daily_quota_for_prompt_usage"("user_uuid_param" "uuid", "prompt_id_param" "uuid", "access_password" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
  author_email TEXT;
  author_user_id UUID;
  uuid_string TEXT;
  prompt_uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid_param IS NULL THEN
    RAISE EXCEPTION 'user_uuid_param cannot be null';
  END IF;
  
  IF prompt_id_param IS NULL THEN
    RAISE EXCEPTION 'prompt_id_param cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid_param::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid user UUID format';
  END IF;
  
  prompt_uuid_string := prompt_id_param::text;
  IF LENGTH(prompt_uuid_string) != 36 OR 
     prompt_uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid prompt UUID format';
  END IF;
  
  -- 获取提示词作者的邮箱地址（因为created_by字段存储的是邮箱）
  SELECT created_by INTO author_email
  FROM "propmt-zhanshi"
  WHERE id = prompt_id_param;
  
  -- 如果提示词不存在
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 通过邮箱地址查找作者的UUID
  SELECT user_id INTO author_user_id
  FROM "membership-true"
  WHERE email = author_email AND is_verified = TRUE;
  
  -- 如果作者不存在或未认证
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 如果是作者自己使用自己的提示词，不给奖励
  IF author_user_id = user_uuid_param THEN
    RETURN FALSE;
  END IF;
  
  -- 为作者添加1次奖励额度（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + 1,
    updated_at = NOW()
  WHERE user_id = author_user_id AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$_$;


ALTER FUNCTION "public"."add_author_daily_quota_for_prompt_usage"("user_uuid_param" "uuid", "prompt_id_param" "uuid", "access_password" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."add_reward_quota"("user_uuid" "uuid", "reward_amount" integer, "access_password" "text") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
  uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF reward_amount IS NULL OR reward_amount <= 0 THEN
    RAISE EXCEPTION 'reward_amount must be positive';
  END IF;
  
  -- 防止恶意大量添加
  IF reward_amount > 50 THEN
    RAISE EXCEPTION 'reward_amount too large (max: 50)';
  END IF;
  
  -- 添加奖励额度（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + reward_amount,
    updated_at = NOW()
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$_$;


ALTER FUNCTION "public"."add_reward_quota"("user_uuid" "uuid", "reward_amount" integer, "access_password" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."create_user_membership"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- 为新注册用户创建免费会员记录
  BEGIN
    INSERT INTO public."membership-true" (user_id, email, membership_level, word_count_used, word_count_limit)
    VALUES (NEW.id, NEW.email, '免费', 0, 0);
  EXCEPTION 
    WHEN unique_violation THEN
      -- 如果邮箱已存在，忽略错误
      NULL;
  END;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."create_user_membership"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."create_user_prompt"("title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "user_display_name_param" "text", "access_password" "text") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    new_prompt_id UUID;
    result JSON;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- 参数验证
    IF title_param IS NULL OR LENGTH(TRIM(title_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词标题不能为空',
            'code', 'INVALID_TITLE'
        );
    END IF;
    
    IF type_param IS NULL OR LENGTH(TRIM(type_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词类型不能为空',
            'code', 'INVALID_TYPE'
        );
    END IF;
    
    IF content_param IS NULL OR LENGTH(TRIM(content_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不能为空',
            'code', 'INVALID_CONTENT'
        );
    END IF;
    
    IF user_email_param IS NULL OR LENGTH(TRIM(user_email_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '用户邮箱不能为空',
            'code', 'INVALID_USER_EMAIL'
        );
    END IF;
    
    IF user_display_name_param IS NULL OR LENGTH(TRIM(user_display_name_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '用户显示名称不能为空',
            'code', 'INVALID_USER_DISPLAY_NAME'
        );
    END IF;
    
    -- 内容长度限制（一万字）
    IF LENGTH(content_param) > 10000 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不能超过10000字',
            'code', 'CONTENT_TOO_LONG'
        );
    END IF;
    
    -- 生成新的UUID
    new_prompt_id := gen_random_uuid();
    
    -- 插入到展示表
    INSERT INTO "propmt-zhanshi" (
        id, title, description, category, type, created_by, author_display_id, created_at, updated_at
    ) VALUES (
        new_prompt_id, 
        TRIM(title_param), 
        TRIM(description_param), 
        'userprompt', 
        TRIM(type_param), 
        TRIM(user_email_param),
        TRIM(user_display_name_param),
        NOW(), 
        NOW()
    );
    
    -- 插入到内容表（使用prompt_id作为主键，不再生成独立的id）
    INSERT INTO "propmt-neirong" (
        prompt_id, content, title, author_display_id, created_at, updated_at
    ) VALUES (
        new_prompt_id,
        TRIM(content_param),
        TRIM(title_param),
        TRIM(user_display_name_param),
        NOW(),
        NOW()
    );
    
    -- 构建返回结果
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'id', new_prompt_id,
            'title', TRIM(title_param),
            'description', TRIM(description_param),
            'type', TRIM(type_param),
            'category', 'userprompt',
            'author_display_id', TRIM(user_display_name_param)
        )
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."create_user_prompt"("title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "user_display_name_param" "text", "access_password" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."deduct_daily_free_quota"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
  current_daily_used INTEGER;
  current_daily_quota INTEGER;
  current_reward_used INTEGER;
  current_reward_quota INTEGER;
  last_reset DATE;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
  daily_deduct INTEGER := 0;
  reward_deduct INTEGER := 0;
  result JSON;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF deduct_amount IS NULL OR deduct_amount <= 0 THEN
    RAISE EXCEPTION 'deduct_amount must be positive';
  END IF;
  
  -- 防止恶意大量扣除
  IF deduct_amount > 100 THEN
    RAISE EXCEPTION 'deduct_amount too large (max: 100)';
  END IF;
  
  -- 获取当前额度使用情况（从true表查询）
  SELECT daily_free_used, daily_free_quota, reward_used, reward_quota, last_free_reset_date
  INTO current_daily_used, current_daily_quota, current_reward_used, current_reward_quota, last_reset
  FROM "membership-true"
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 如果用户不存在或未认证
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found or not verified',
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 检查是否需要重置每日额度
  IF last_reset < CURRENT_DATE THEN
    UPDATE "membership-true"
    SET 
      daily_free_used = 0,
      reward_used = 0,  -- 奖励额度也每日重置
      last_free_reset_date = CURRENT_DATE,
      updated_at = NOW()
    WHERE user_id = user_uuid;
    current_daily_used := 0;
    current_reward_used := 0;
  END IF;
  
  -- 计算可用额度
  daily_available := GREATEST(0, current_daily_quota - current_daily_used);
  reward_available := GREATEST(0, current_reward_quota - current_reward_used);
  
  -- 检查总额度是否足够
  IF daily_available + reward_available < deduct_amount THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Insufficient quota',
      'daily_available', daily_available,
      'reward_available', reward_available,
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 优先扣除每日免费额度
  IF daily_available > 0 THEN
    daily_deduct := LEAST(daily_available, deduct_amount);
    deduct_amount := deduct_amount - daily_deduct;
  END IF;
  
  -- 如果还有剩余，扣除奖励额度
  IF deduct_amount > 0 AND reward_available > 0 THEN
    reward_deduct := LEAST(reward_available, deduct_amount);
  END IF;
  
  -- 执行扣除操作（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    daily_free_used = current_daily_used + daily_deduct,
    reward_used = current_reward_used + reward_deduct,
    updated_at = NOW()
  WHERE user_id = user_uuid;
  
  -- 返回扣费结果
  RETURN json_build_object(
    'success', true,
    'message', 'Deduction successful',
    'daily_deducted', daily_deduct,
    'reward_deducted', reward_deduct,
    'used_daily', daily_deduct > 0,
    'used_reward', reward_deduct > 0
  );
END;
$_$;


ALTER FUNCTION "public"."deduct_daily_free_quota"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."deduct_user_word_count"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
    current_used INTEGER;
    current_limit INTEGER;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RAISE EXCEPTION 'Invalid access password';
    END IF;
    
    -- 参数验证
    IF user_uuid IS NULL THEN
        RAISE EXCEPTION 'user_uuid cannot be null';
    END IF;
    
    -- 严格的UUID格式验证
    uuid_string := user_uuid::text;
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RAISE EXCEPTION 'Invalid UUID format';
    END IF;
    
    IF deduct_amount IS NULL OR deduct_amount <= 0 THEN
        RAISE EXCEPTION 'deduct_amount must be positive';
    END IF;
    
    -- 防止恶意大量扣除
    IF deduct_amount > 1000000 THEN
        RAISE EXCEPTION 'deduct_amount too large (max: 1000000)';
    END IF;
    
    -- 获取当前用户的字数使用情况
    SELECT word_count_used, word_count_limit
    INTO current_used, current_limit
    FROM "membership-true"
    WHERE user_id = user_uuid AND is_verified = TRUE;
    
    -- 如果用户不存在或未认证
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- 检查余额是否足够
    IF current_used + deduct_amount > current_limit THEN
        RETURN FALSE;
    END IF;
    
    -- 扣除字数
    UPDATE "membership-true"
    SET 
        word_count_used = current_used + deduct_amount,
        updated_at = NOW()
    WHERE user_id = user_uuid;
    
    RETURN TRUE;
END;
$_$;


ALTER FUNCTION "public"."deduct_user_word_count"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."delete_user_prompt"("prompt_id_param" "uuid", "user_email_param" "text", "access_password" "text" DEFAULT 'PROMPT_ACCESS_2024_SECURE_KEY'::"text") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
    prompt_record RECORD;
    result JSON;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- UUID格式验证
    IF prompt_id_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    uuid_string := prompt_id_param::text;
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    -- 参数验证
    IF user_email_param IS NULL OR LENGTH(TRIM(user_email_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '用户邮箱不能为空',
            'code', 'INVALID_USER_EMAIL'
        );
    END IF;
    
    -- 检查提示词是否存在且用户有权限
    SELECT id, title, created_by INTO prompt_record
    FROM "propmt-zhanshi"
    WHERE id = prompt_id_param AND created_by = TRIM(user_email_param);
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词不存在或无权限删除',
            'code', 'PERMISSION_DENIED'
        );
    END IF;
    
    -- 删除内容表记录（使用prompt_id作为主键）
    DELETE FROM "propmt-neirong" WHERE prompt_id = prompt_id_param;
    
    -- 删除展示表记录
    DELETE FROM "propmt-zhanshi" WHERE id = prompt_id_param;
    
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'id', prompt_id_param,
            'title', prompt_record.title,
            'message', '提示词删除成功'
        )
    );
    
    RETURN result;
END;
$_$;


ALTER FUNCTION "public"."delete_user_prompt"("prompt_id_param" "uuid", "user_email_param" "text", "access_password" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."get_prompt_content"("prompt_id_param" "uuid", "access_password" "text", "user_email_param" "text" DEFAULT NULL::"text") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
    prompt_record RECORD;
    content_record RECORD;
    result JSON;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- 严格的UUID格式验证
    IF prompt_id_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    -- 将UUID转换为字符串进行格式检查
    uuid_string := prompt_id_param::text;
    
    -- 检查UUID字符串格式
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    -- 检查提示词是否存在
    SELECT id, title, description, type, category, created_by
    INTO prompt_record
    FROM "propmt-zhanshi"
    WHERE id = prompt_id_param;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词不存在',
            'code', 'PROMPT_NOT_FOUND'
        );
    END IF;
    
    -- 修改权限验证：允许认证用户使用他人的提示词
    IF prompt_record.category = 'userprompt' AND user_email_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '需要登录才能使用用户提示词',
            'code', 'LOGIN_REQUIRED'
        );
    END IF;
    
    -- 获取提示词内容并增加使用次数（直接使用prompt_id作为主键查询）
    UPDATE "propmt-neirong" 
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE prompt_id = prompt_id_param
    RETURNING content, title, usage_count INTO content_record;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不存在',
            'code', 'CONTENT_NOT_FOUND'
        );
    END IF;
    
    -- 构建返回结果
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'id', prompt_record.id,
            'title', content_record.title,
            'description', prompt_record.description,
            'type', prompt_record.type,
            'category', prompt_record.category,
            'content', content_record.content,
            'created_by', prompt_record.created_by,
            'usage_count', content_record.usage_count
        )
    );
    
    RETURN result;
END;
$_$;


ALTER FUNCTION "public"."get_prompt_content"("prompt_id_param" "uuid", "access_password" "text", "user_email_param" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."get_user_quota_info"("user_uuid" "uuid", "access_password" "text") RETURNS "json"
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
  user_info RECORD;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  -- 获取用户额度信息（从look表查询）
  SELECT 
    daily_free_quota,
    daily_free_used,
    reward_quota,
    reward_used,
    last_free_reset_date,
    is_verified
  INTO user_info
  FROM "membership-look"
  WHERE user_id = user_uuid;
  
  -- 如果用户不存在
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found'
    );
  END IF;
  
  -- 检查是否需要重置（但不实际重置，只计算）
  IF user_info.last_free_reset_date < CURRENT_DATE THEN
    daily_available := user_info.daily_free_quota;
    reward_available := user_info.reward_quota;
  ELSE
    daily_available := GREATEST(0, user_info.daily_free_quota - user_info.daily_free_used);
    reward_available := GREATEST(0, user_info.reward_quota - user_info.reward_used);
  END IF;
  
  -- 返回完整信息
  RETURN json_build_object(
    'success', true,
    'is_verified', user_info.is_verified,
    'daily_free_quota', user_info.daily_free_quota,
    'daily_free_used', CASE WHEN user_info.last_free_reset_date < CURRENT_DATE THEN 0 ELSE user_info.daily_free_used END,
    'daily_free_remaining', daily_available,
    'reward_quota', user_info.reward_quota,
    'reward_used', CASE WHEN user_info.last_free_reset_date < CURRENT_DATE THEN 0 ELSE user_info.reward_used END,
    'reward_remaining', reward_available,
    'last_reset_date', user_info.last_free_reset_date,
    'needs_reset', user_info.last_free_reset_date < CURRENT_DATE
  );
END;
$_$;


ALTER FUNCTION "public"."get_user_quota_info"("user_uuid" "uuid", "access_password" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."increment_prompt_usage"("prompt_id_param" "uuid", "access_password" "text") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    result JSON;
    updated_count INTEGER;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- UUID格式验证
    IF prompt_id_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词ID不能为空',
            'code', 'INVALID_PROMPT_ID'
        );
    END IF;
    
    -- 增加使用次数
    UPDATE "propmt-neirong" 
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE prompt_id = prompt_id_param
    RETURNING usage_count INTO updated_count;
    
    -- 检查是否找到记录
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不存在',
            'code', 'CONTENT_NOT_FOUND'
        );
    END IF;
    
    -- 构建返回结果
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'prompt_id', prompt_id_param,
            'usage_count', updated_count
        )
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."increment_prompt_usage"("prompt_id_param" "uuid", "access_password" "text") OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."reset_all_user_quotas"() RETURNS integer
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  reset_count INTEGER := 0;
BEGIN
  -- 重置所有用户的免费额度和奖励额度
  UPDATE "membership-true"
  SET 
    daily_free_used = 0,
    daily_free_quota = 25,
    reward_quota = 0,
    reward_used = 0,
    last_free_reset_date = CURRENT_DATE,
    updated_at = NOW();
  
  GET DIAGNOSTICS reset_count = ROW_COUNT;
  
  -- 记录操作日志
  RAISE NOTICE '重置了 % 个用户的额度配置', reset_count;
  
  RETURN reset_count;
END;
$$;


ALTER FUNCTION "public"."reset_all_user_quotas"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."set_initial_title"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    prompt_title TEXT;
BEGIN
    -- 获取对应的提示词标题
    SELECT title INTO prompt_title
    FROM "propmt-zhanshi"
    WHERE id = NEW.prompt_id;
    
    -- 如果标题为空，设置为获取到的标题
    IF NEW.title = '' OR NEW.title IS NULL THEN
        NEW.title := COALESCE(prompt_title, '');
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_initial_title"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."set_user_verification_status"("user_uuid" "uuid", "verified_status" boolean, "free_quota" integer DEFAULT 50) RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
BEGIN
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 验证UUID格式
  IF NOT (user_uuid::text ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$') THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  -- 更新认证状态
  UPDATE "membership-true"
  SET 
    is_verified = verified_status,
    daily_free_quota = CASE WHEN verified_status THEN free_quota ELSE 0 END,
    daily_free_used = 0,
    last_free_reset_date = CURRENT_DATE,
    updated_at = NOW()
  WHERE user_id = user_uuid;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$_$;


ALTER FUNCTION "public"."set_user_verification_status"("user_uuid" "uuid", "verified_status" boolean, "free_quota" integer) OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."sync_membership_to_look"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO public."membership-look" VALUES (NEW.*)
    ON CONFLICT (id) DO NOTHING;
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    UPDATE public."membership-look" SET 
      email = NEW.email,
      membership_level = NEW.membership_level,
      word_count_limit = NEW.word_count_limit,
      word_count_used = NEW.word_count_used,
      created_at = NEW.created_at,
      updated_at = NEW.updated_at,
      user_id = NEW.user_id,
      is_verified = NEW.is_verified,
      daily_free_quota = NEW.daily_free_quota,
      daily_free_used = NEW.daily_free_used,
      last_free_reset_date = NEW.last_free_reset_date,
      reward_quota = NEW.reward_quota,
      reward_used = NEW.reward_used
    WHERE id = NEW.id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    DELETE FROM public."membership-look" WHERE id = OLD.id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."sync_membership_to_look"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."sync_prompt_title"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- 当 propmt-zhanshi 表的标题更新时，同步更新 propmt-neirong 表
    IF TG_OP = 'UPDATE' AND OLD.title IS DISTINCT FROM NEW.title THEN
        UPDATE "propmt-neirong" 
        SET title = NEW.title,
            updated_at = NOW()
        WHERE prompt_id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."sync_prompt_title"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."sync_usage_count_to_zhanshi"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- 当propmt-neirong表的usage_count字段更新时，同步更新propmt-zhanshi表
    IF TG_OP = 'UPDATE' AND OLD.usage_count IS DISTINCT FROM NEW.usage_count THEN
        UPDATE "propmt-zhanshi" 
        SET usage_count = NEW.usage_count,
            updated_at = NOW()
        WHERE id = NEW.prompt_id;
    END IF;
    
    -- 当插入新记录时，也要同步usage_count（虽然通常是0）
    IF TG_OP = 'INSERT' THEN
        UPDATE "propmt-zhanshi" 
        SET usage_count = NEW.usage_count,
            updated_at = NOW()
        WHERE id = NEW.prompt_id;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."sync_usage_count_to_zhanshi"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."update_user_prompt"("prompt_id_param" "uuid", "title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "access_password" "text" DEFAULT 'PROMPT_ACCESS_2024_SECURE_KEY'::"text") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
    prompt_record RECORD;
    result JSON;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- UUID格式验证
    IF prompt_id_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    uuid_string := prompt_id_param::text;
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    -- 参数验证
    IF title_param IS NULL OR LENGTH(TRIM(title_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词标题不能为空',
            'code', 'INVALID_TITLE'
        );
    END IF;
    
    IF content_param IS NULL OR LENGTH(TRIM(content_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不能为空',
            'code', 'INVALID_CONTENT'
        );
    END IF;
    
    IF user_email_param IS NULL OR LENGTH(TRIM(user_email_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '用户邮箱不能为空',
            'code', 'INVALID_USER_EMAIL'
        );
    END IF;
    
    -- 内容长度限制
    IF LENGTH(content_param) > 10000 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不能超过10000字',
            'code', 'CONTENT_TOO_LONG'
        );
    END IF;
    
    -- 检查提示词是否存在且用户有权限
    SELECT id, created_by INTO prompt_record
    FROM "propmt-zhanshi"
    WHERE id = prompt_id_param AND created_by = TRIM(user_email_param);
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词不存在或无权限修改',
            'code', 'PERMISSION_DENIED'
        );
    END IF;
    
    -- 更新展示表
    UPDATE "propmt-zhanshi" 
    SET 
        title = TRIM(title_param),
        description = TRIM(description_param),
        type = TRIM(type_param),
        updated_at = NOW()
    WHERE id = prompt_id_param;
    
    -- 更新内容表（使用prompt_id作为主键查询）
    UPDATE "propmt-neirong" 
    SET 
        content = TRIM(content_param),
        title = TRIM(title_param),
        updated_at = NOW()
    WHERE prompt_id = prompt_id_param;
    
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'id', prompt_id_param,
            'title', TRIM(title_param),
            'description', TRIM(description_param),
            'type', TRIM(type_param)
        )
    );
    
    RETURN result;
END;
$_$;


ALTER FUNCTION "public"."update_user_prompt"("prompt_id_param" "uuid", "title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "access_password" "text") OWNER TO "supabase_admin";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."membership-look" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "email" character varying NOT NULL,
    "membership_level" character varying DEFAULT '免费'::character varying NOT NULL,
    "word_count_limit" integer,
    "word_count_used" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "user_id" "uuid",
    "is_verified" boolean DEFAULT false,
    "daily_free_quota" integer DEFAULT 0,
    "daily_free_used" integer DEFAULT 0,
    "last_free_reset_date" "date" DEFAULT CURRENT_DATE,
    "reward_quota" integer DEFAULT 0,
    "reward_used" integer DEFAULT 0
);


ALTER TABLE "public"."membership-look" OWNER TO "supabase_admin";


COMMENT ON COLUMN "public"."membership-look"."reward_quota" IS '奖励额度总量（从true表同步）';



COMMENT ON COLUMN "public"."membership-look"."reward_used" IS '奖励额度已使用量（从true表同步）';



CREATE TABLE IF NOT EXISTS "public"."membership-true" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "email" character varying NOT NULL,
    "membership_level" character varying DEFAULT '免费'::character varying NOT NULL,
    "word_count_limit" integer,
    "word_count_used" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "user_id" "uuid",
    "is_verified" boolean DEFAULT false,
    "daily_free_quota" integer DEFAULT 0,
    "daily_free_used" integer DEFAULT 0,
    "last_free_reset_date" "date" DEFAULT CURRENT_DATE,
    "reward_quota" integer DEFAULT 0,
    "reward_used" integer DEFAULT 0
);


ALTER TABLE "public"."membership-true" OWNER TO "supabase_admin";


COMMENT ON COLUMN "public"."membership-true"."reward_quota" IS '奖励额度总量';



COMMENT ON COLUMN "public"."membership-true"."reward_used" IS '奖励额度已使用量';



CREATE TABLE IF NOT EXISTS "public"."novel_files" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "file_name" "text" NOT NULL,
    "file_path" "text" NOT NULL,
    "file_size" bigint,
    "mime_type" "text" DEFAULT 'text/plain'::"text",
    "upload_time" timestamp with time zone DEFAULT "now"(),
    "minio_bucket" "text" DEFAULT 'xiaoshuo'::"text",
    "minio_object_key" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "work_title" "text"
);


ALTER TABLE "public"."novel_files" OWNER TO "supabase_admin";


CREATE TABLE IF NOT EXISTS "public"."propmt-neirong" (
    "prompt_id" "uuid" NOT NULL,
    "content" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "title" "text" DEFAULT ''::"text" NOT NULL,
    "author_display_id" character varying(100),
    "usage_count" integer DEFAULT 0 NOT NULL
);


ALTER TABLE "public"."propmt-neirong" OWNER TO "supabase_admin";


COMMENT ON COLUMN "public"."propmt-neirong"."title" IS '提示词标题名称';



CREATE TABLE IF NOT EXISTS "public"."propmt-zhanshi" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" character varying(255) NOT NULL,
    "description" "text",
    "category" character varying(100) DEFAULT 'official'::character varying NOT NULL,
    "type" character varying(50) NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "created_by" character varying(255),
    "author_display_id" character varying(100),
    "usage_count" integer DEFAULT 0 NOT NULL
);


ALTER TABLE "public"."propmt-zhanshi" OWNER TO "supabase_admin";


ALTER TABLE ONLY "public"."membership-look"
    ADD CONSTRAINT "membership-look_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."membership-look"
    ADD CONSTRAINT "membership-look_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."membership-true"
    ADD CONSTRAINT "membership_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."membership-true"
    ADD CONSTRAINT "membership_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."novel_files"
    ADD CONSTRAINT "novel_files_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."propmt-neirong"
    ADD CONSTRAINT "propmt-neirong_pkey" PRIMARY KEY ("prompt_id");



ALTER TABLE ONLY "public"."propmt-zhanshi"
    ADD CONSTRAINT "propmt-zhanshi_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_membership_user_id" ON "public"."membership-true" USING "btree" ("user_id");



CREATE INDEX "idx_novel_files_minio_object_key" ON "public"."novel_files" USING "btree" ("minio_object_key");



CREATE INDEX "idx_novel_files_user_id" ON "public"."novel_files" USING "btree" ("user_id");



CREATE INDEX "idx_propmt_neirong_prompt_id" ON "public"."propmt-neirong" USING "btree" ("prompt_id");



CREATE INDEX "idx_propmt_neirong_usage_count" ON "public"."propmt-neirong" USING "btree" ("usage_count" DESC);



CREATE INDEX "idx_propmt_zhanshi_category" ON "public"."propmt-zhanshi" USING "btree" ("category");



CREATE INDEX "idx_propmt_zhanshi_type" ON "public"."propmt-zhanshi" USING "btree" ("type");



CREATE INDEX "idx_propmt_zhanshi_usage_count" ON "public"."propmt-zhanshi" USING "btree" ("usage_count" DESC);



CREATE INDEX "membership-look_user_id_idx" ON "public"."membership-look" USING "btree" ("user_id");



CREATE OR REPLACE TRIGGER "set_initial_title_trigger" BEFORE INSERT ON "public"."propmt-neirong" FOR EACH ROW EXECUTE FUNCTION "public"."set_initial_title"();



CREATE OR REPLACE TRIGGER "sync_membership_trigger" AFTER INSERT OR DELETE OR UPDATE ON "public"."membership-true" FOR EACH ROW EXECUTE FUNCTION "public"."sync_membership_to_look"();



CREATE OR REPLACE TRIGGER "sync_title_trigger" AFTER UPDATE ON "public"."propmt-zhanshi" FOR EACH ROW EXECUTE FUNCTION "public"."sync_prompt_title"();



CREATE OR REPLACE TRIGGER "trigger_sync_usage_count_to_zhanshi" AFTER INSERT OR UPDATE ON "public"."propmt-neirong" FOR EACH ROW EXECUTE FUNCTION "public"."sync_usage_count_to_zhanshi"();



ALTER TABLE ONLY "public"."novel_files"
    ADD CONSTRAINT "novel_files_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."propmt-neirong"
    ADD CONSTRAINT "propmt-neirong_prompt_id_fkey" FOREIGN KEY ("prompt_id") REFERENCES "public"."propmt-zhanshi"("id") ON DELETE CASCADE;



CREATE POLICY "Allow read access for all users" ON "public"."propmt-zhanshi" FOR SELECT USING (true);



CREATE POLICY "Allow write access for specific user" ON "public"."propmt-zhanshi" USING ((("auth"."jwt"() ->> 'email'::"text") = '<EMAIL>'::"text"));



CREATE POLICY "Deny all access" ON "public"."propmt-neirong" USING (false) WITH CHECK (false);



CREATE POLICY "Service role full access on membership-true" ON "public"."membership-true" USING (("auth"."role"() = 'service_role'::"text"));



CREATE POLICY "Users can view own membership-look by uuid" ON "public"."membership-look" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "allow_function_access" ON "public"."propmt-neirong" TO "postgres" USING (true) WITH CHECK (true);



ALTER TABLE "public"."membership-look" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."membership-true" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."propmt-neirong" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."propmt-zhanshi" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "users_can_access_own_prompt_content" ON "public"."propmt-neirong" USING ((EXISTS ( SELECT 1
   FROM "public"."propmt-zhanshi"
  WHERE (("propmt-zhanshi"."id" = "propmt-neirong"."prompt_id") AND ((("propmt-zhanshi"."category")::"text" = 'official'::"text") OR ((("propmt-zhanshi"."category")::"text" = 'userprompt'::"text") AND (("propmt-zhanshi"."created_by")::"text" = ("auth"."jwt"() ->> 'email'::"text"))))))));





ALTER PUBLICATION "supabase_realtime" OWNER TO "supabase_admin";








GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";
































































































































































































GRANT ALL ON FUNCTION "public"."add_author_daily_quota_for_prompt_usage"("user_uuid_param" "uuid", "prompt_id_param" "uuid", "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."add_author_daily_quota_for_prompt_usage"("user_uuid_param" "uuid", "prompt_id_param" "uuid", "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."add_author_daily_quota_for_prompt_usage"("user_uuid_param" "uuid", "prompt_id_param" "uuid", "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_author_daily_quota_for_prompt_usage"("user_uuid_param" "uuid", "prompt_id_param" "uuid", "access_password" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_reward_quota"("user_uuid" "uuid", "reward_amount" integer, "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."add_reward_quota"("user_uuid" "uuid", "reward_amount" integer, "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."add_reward_quota"("user_uuid" "uuid", "reward_amount" integer, "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_reward_quota"("user_uuid" "uuid", "reward_amount" integer, "access_password" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_user_membership"() TO "postgres";
GRANT ALL ON FUNCTION "public"."create_user_membership"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_user_membership"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_user_membership"() TO "service_role";



GRANT ALL ON FUNCTION "public"."create_user_prompt"("title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "user_display_name_param" "text", "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."create_user_prompt"("title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "user_display_name_param" "text", "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_user_prompt"("title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "user_display_name_param" "text", "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_user_prompt"("title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "user_display_name_param" "text", "access_password" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."deduct_daily_free_quota"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."deduct_daily_free_quota"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."deduct_daily_free_quota"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."deduct_daily_free_quota"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."deduct_user_word_count"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."deduct_user_word_count"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."deduct_user_word_count"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."deduct_user_word_count"("user_uuid" "uuid", "deduct_amount" integer, "access_password" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."delete_user_prompt"("prompt_id_param" "uuid", "user_email_param" "text", "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."delete_user_prompt"("prompt_id_param" "uuid", "user_email_param" "text", "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."delete_user_prompt"("prompt_id_param" "uuid", "user_email_param" "text", "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."delete_user_prompt"("prompt_id_param" "uuid", "user_email_param" "text", "access_password" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_prompt_content"("prompt_id_param" "uuid", "access_password" "text", "user_email_param" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."get_prompt_content"("prompt_id_param" "uuid", "access_password" "text", "user_email_param" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_prompt_content"("prompt_id_param" "uuid", "access_password" "text", "user_email_param" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_prompt_content"("prompt_id_param" "uuid", "access_password" "text", "user_email_param" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_quota_info"("user_uuid" "uuid", "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."get_user_quota_info"("user_uuid" "uuid", "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_quota_info"("user_uuid" "uuid", "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_quota_info"("user_uuid" "uuid", "access_password" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_prompt_usage"("prompt_id_param" "uuid", "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."increment_prompt_usage"("prompt_id_param" "uuid", "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."increment_prompt_usage"("prompt_id_param" "uuid", "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."increment_prompt_usage"("prompt_id_param" "uuid", "access_password" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."reset_all_user_quotas"() TO "postgres";
GRANT ALL ON FUNCTION "public"."reset_all_user_quotas"() TO "anon";
GRANT ALL ON FUNCTION "public"."reset_all_user_quotas"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."reset_all_user_quotas"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_initial_title"() TO "postgres";
GRANT ALL ON FUNCTION "public"."set_initial_title"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_initial_title"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_initial_title"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_user_verification_status"("user_uuid" "uuid", "verified_status" boolean, "free_quota" integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."set_user_verification_status"("user_uuid" "uuid", "verified_status" boolean, "free_quota" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."set_user_verification_status"("user_uuid" "uuid", "verified_status" boolean, "free_quota" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_user_verification_status"("user_uuid" "uuid", "verified_status" boolean, "free_quota" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_membership_to_look"() TO "postgres";
GRANT ALL ON FUNCTION "public"."sync_membership_to_look"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_membership_to_look"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_membership_to_look"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_prompt_title"() TO "postgres";
GRANT ALL ON FUNCTION "public"."sync_prompt_title"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_prompt_title"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_prompt_title"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_usage_count_to_zhanshi"() TO "postgres";
GRANT ALL ON FUNCTION "public"."sync_usage_count_to_zhanshi"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_usage_count_to_zhanshi"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_usage_count_to_zhanshi"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_user_prompt"("prompt_id_param" "uuid", "title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "access_password" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."update_user_prompt"("prompt_id_param" "uuid", "title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "access_password" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_user_prompt"("prompt_id_param" "uuid", "title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "access_password" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_user_prompt"("prompt_id_param" "uuid", "title_param" "text", "description_param" "text", "type_param" "text", "content_param" "text", "user_email_param" "text", "access_password" "text") TO "service_role";
























GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."membership-look" TO "postgres";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."membership-look" TO "anon";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."membership-look" TO "authenticated";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."membership-look" TO "service_role";



GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."membership-true" TO "postgres";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."membership-true" TO "anon";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."membership-true" TO "authenticated";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."membership-true" TO "service_role";



GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."novel_files" TO "postgres";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."novel_files" TO "anon";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."novel_files" TO "authenticated";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."novel_files" TO "service_role";



GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."propmt-neirong" TO "postgres";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."propmt-neirong" TO "anon";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."propmt-neirong" TO "authenticated";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."propmt-neirong" TO "service_role";



GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."propmt-zhanshi" TO "postgres";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."propmt-zhanshi" TO "anon";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."propmt-zhanshi" TO "authenticated";
GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "public"."propmt-zhanshi" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLES TO "service_role";






























RESET ALL;
